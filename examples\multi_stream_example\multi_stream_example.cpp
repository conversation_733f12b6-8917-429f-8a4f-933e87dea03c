#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <memory>
#include <fstream>
#include <filesystem>
#include <opencv2/opencv.hpp>

#include "core/video_processing_core.h"
#include "core/project_manager.h"
#include "core/project.h"
#include "utils/cv_utils.h"
#include "utils/plugin_renderer.h"
#include "utils/string_utils.h"
#include "ai/plugins/python_script_manager.h"
#include "ai/plugins/python_script_plugin.h"
#include <json/json.h>

#ifdef _WIN32
#include <windows.h>
#endif

// 视频流信息结构体
struct StreamInfo {
    int id;                                             // 流ID
    std::string projectPath;                            // 项目文件路径
    std::shared_ptr<core::Project> project;             // 项目对象
    std::shared_ptr<core::VideoProcessingCore> processor; // 视频处理器
    std::shared_ptr<ai::plugins::PythonScriptManager> scriptManager; // Python脚本管理器
    std::string videoUrl;                               // 视频URL
    bool isActive;                                      // 是否激活
    bool isPaused;                                      // 是否暂停
    int resultStoragePort;                              // 结果存储服务器端口
    bool isVideoEnded;                                  // 视频是否已结束
    bool hasError;                                      // 是否发生错误

    // 性能监控
    double fps;                                         // 当前FPS
    double processingTime;                              // 处理时间(ms)
    int frameCount;                                     // 已处理帧数
    std::chrono::time_point<std::chrono::high_resolution_clock> lastFrameTime; // 上一帧时间

    // 构造函数，初始化性能监控字段
    StreamInfo() : id(0), isActive(false), isPaused(false), isVideoEnded(false), hasError(false),
                  resultStoragePort(8888), fps(0.0), processingTime(0.0), frameCount(0) {}
};

// 重新加载视频流
bool reload_stream(StreamInfo& stream_info) {
    if (!stream_info.isActive || !stream_info.processor) {
        return false;
    }

    // 关闭当前视频源
    stream_info.processor->close_video();

    // 重新打开视频源
    bool opened = false;
    std::string url = stream_info.videoUrl;

    if (url.find("rtsp://") == 0) {
        // RTSP流
        opened = stream_info.processor->open_rtsp_stream(url);
        std::cout << "视频流 #" << (stream_info.id + 1) << " 重新加载RTSP流: " << url << std::endl;
    } else {
        // 视频文件
        opened = stream_info.processor->open_video_file(url);
        std::cout << "视频流 #" << (stream_info.id + 1) << " 重新加载视频文件: " << url << std::endl;
    }

    if (opened) {
        // 重置性能监控和状态
        stream_info.fps = 0.0;
        stream_info.processingTime = 0.0;
        stream_info.frameCount = 0;
        stream_info.isPaused = false;
        stream_info.isVideoEnded = false;  // 重置视频结束标志
        stream_info.hasError = false;      // 重置错误标志
        stream_info.isActive = true;       // 设置为活动状态

        return true;
    } else {
        std::cerr << "视频流 #" << (stream_info.id + 1) << " 重新加载失败: " << url << std::endl;
        return false;
    }
}

// 保存视频流信息到JSON文件
bool save_stream_info_to_json(const std::vector<StreamInfo>& streams, const std::string& filename) {
    Json::Value root;

    // 添加基本信息
    root["timestamp"] = Json::Value(static_cast<Json::UInt64>(
        std::chrono::system_clock::now().time_since_epoch().count()));
    root["stream_count"] = static_cast<int>(streams.size());

    // 添加每个视频流的信息
    Json::Value streamsArray(Json::arrayValue);
    for (const auto& stream : streams) {
        Json::Value streamObj;
        streamObj["id"] = stream.id;
        streamObj["project_path"] = stream.projectPath;
        streamObj["is_active"] = stream.isActive;
        streamObj["is_paused"] = stream.isPaused;
        streamObj["is_video_ended"] = stream.isVideoEnded;
        streamObj["has_error"] = stream.hasError;
        streamObj["result_storage_port"] = stream.resultStoragePort;

        if (stream.isActive && stream.project) {
            streamObj["project_name"] = stream.project->get_name();
            streamObj["video_url"] = stream.videoUrl;
            streamObj["fps"] = stream.fps;
            streamObj["processing_time_ms"] = stream.processingTime;
            streamObj["frame_count"] = stream.frameCount;

            // 添加结果存储服务器信息
            auto resultServer = stream.processor->get_result_storage_server();
            if (resultServer) {
                streamObj["result_server_running"] = resultServer->is_running();
                streamObj["result_server_port"] = resultServer->get_port();
                streamObj["result_server_client_count"] = resultServer->get_client_count();
                streamObj["result_server_storage_path"] = resultServer->get_storage_path();
            }
        }

        streamsArray.append(streamObj);
    }
    root["streams"] = streamsArray;

    // 写入文件
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            std::cerr << "无法打开文件进行写入: " << filename << std::endl;
            return false;
        }

        Json::StyledWriter writer;
        file << writer.write(root);
        file.close();

        return true;
    } catch (const std::exception& e) {
        std::cerr << "保存JSON文件时发生异常: " << e.what() << std::endl;
        return false;
    }
}

// 视频流处理线程函数
void process_stream(StreamInfo& stream_info,
                   std::atomic<bool>& running,
                   std::mutex& display_mutex,
                   cv::Mat& display_frame,
                   const cv::Rect& roi) {

    utils::PluginRenderer renderer;

    // 初始化性能监控
    auto lastFpsUpdateTime = std::chrono::high_resolution_clock::now();
    int framesSinceLastFpsUpdate = 0;

    // 连续错误计数
    int consecutiveErrorCount = 0;
    const int MAX_CONSECUTIVE_ERRORS = 5;

    while (running) {
        try {
            // 如果视频已结束或发生严重错误，显示相应信息并等待
            if (stream_info.isVideoEnded || stream_info.hasError) {
                std::lock_guard<std::mutex> lock(display_mutex);
                cv::Mat endFrame(roi.height, roi.width, CV_8UC3, cv::Scalar(0, 0, 0));
                std::string endMsg;

                if (stream_info.isVideoEnded) {
                    endMsg = "视频播放结束";
                    utils::putTextZH(endFrame, endMsg,
                               cv::Point(20, roi.height/2),
                               0.7,
                               cv::Scalar(255, 255, 255), 2);
                } else {
                    endMsg = "视频处理错误";
                    utils::putTextZH(endFrame, endMsg,
                               cv::Point(20, roi.height/2),
                               0.7,
                               cv::Scalar(0, 0, 255), 2);
                }

                // 显示重新加载提示
                std::string reloadMsg = "按Shift+" + std::to_string(stream_info.id + 1) + "重新加载";
                utils::putTextZH(endFrame, reloadMsg,
                           cv::Point(20, roi.height/2 + 40),
                           0.5,
                           cv::Scalar(0, 255, 255), 1);

                endFrame.copyTo(display_frame(roi));

                // 等待一段时间后再检查running状态
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                continue;
            }

            // 如果暂停，则等待
            if (stream_info.isPaused) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                continue;
            }

            // 记录开始处理时间
            auto startTime = std::chrono::high_resolution_clock::now();

            // 处理当前帧
            ai::FrameResult result;
            try {
                // 处理下一帧
                result = stream_info.processor->process_next_frame();
                // 成功处理帧，重置错误计数
                consecutiveErrorCount = 0;
            } catch (const std::runtime_error& e) {
                // 检查是否是"无法读取下一帧"错误，这通常表示视频结束
                std::string error = e.what();
                if (error.find("无法读取下一帧") != std::string::npos) {
                    std::cout << "视频流 #" << (stream_info.id + 1) << " 播放结束" << std::endl;
                    stream_info.isVideoEnded = true;
                    continue;
                } else {
                    // 其他错误，增加错误计数
                    consecutiveErrorCount++;
                    if (consecutiveErrorCount >= MAX_CONSECUTIVE_ERRORS) {
                        std::cerr << "视频流 #" << (stream_info.id + 1) << " 连续错误次数过多，标记为错误状态" << std::endl;
                        stream_info.hasError = true;
                        continue;
                    }
                    throw; // 重新抛出异常，让外层catch处理
                }
            }

            // 记录结束处理时间
            auto endTime = std::chrono::high_resolution_clock::now();

            // 计算处理时间(毫秒)
            stream_info.processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

            // 更新帧计数
            stream_info.frameCount++;
            framesSinceLastFpsUpdate++;

            // 每秒更新一次FPS
            auto now = std::chrono::high_resolution_clock::now();
            auto timeSinceLastFpsUpdate = std::chrono::duration<double>(now - lastFpsUpdateTime).count();
            if (timeSinceLastFpsUpdate >= 1.0) {
                stream_info.fps = framesSinceLastFpsUpdate / timeSinceLastFpsUpdate;
                lastFpsUpdateTime = now;
                framesSinceLastFpsUpdate = 0;
            }

            // 获取处理后的帧
            cv::Mat processedFrame = result.processed_frame;

            // 如果处理后的帧为空，跳过
            if (processedFrame.empty()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(30));
                continue;
            }

            // 渲染结果
            renderer.render_frame_result(processedFrame, result);

            // 调整大小以适应显示区域
            cv::Mat resizedFrame;
            cv::resize(processedFrame, resizedFrame, cv::Size(roi.width, roi.height));

            // 将处理后的帧复制到显示帧的相应区域
            {
                std::lock_guard<std::mutex> lock(display_mutex);
                resizedFrame.copyTo(display_frame(roi));

                // 计算文本位置 - 左下角
                int textStartX = roi.x + 10;
                int textStartY = roi.y + roi.height - 180; // 从底部向上180像素开始，为了增加一行结果服务器信息

                // 添加视频流标识和项目名称
                std::string label = "视频流 #" + std::to_string(stream_info.id + 1);
                if (stream_info.isPaused) {
                    label += " [已暂停]";
                }
                utils::putTextZH(display_frame, label,
                           cv::Point(textStartX, textStartY),
                            0.7,
                           cv::Scalar(0, 255, 0), 2);

                // 添加项目名称
                std::string projectName = "项目: " + stream_info.project->get_name();
                utils::putTextZH(display_frame, projectName,
                           cv::Point(textStartX, textStartY + 30),
                           0.5,
                           cv::Scalar(0, 255, 255), 1);

                // 添加结果服务器信息
                std::string serverInfo = "结果服务器端口: " + std::to_string(stream_info.resultStoragePort);
                utils::putTextZH(display_frame, serverInfo,
                           cv::Point(textStartX, textStartY + 60),
                           0.5,
                           cv::Scalar(255, 0, 255), 1);

                // 添加性能信息
                std::string fpsInfo = "FPS: " + std::to_string(int(stream_info.fps));
                utils::putTextZH(display_frame, fpsInfo,
                           cv::Point(textStartX, textStartY + 90),
                           0.5,
                           cv::Scalar(255, 255, 0), 1);

                std::string timeInfo = "处理时间: " + std::to_string(int(stream_info.processingTime)) + " ms";
                utils::putTextZH(display_frame, timeInfo,
                           cv::Point(textStartX, textStartY + 120),
                           0.5,
                           cv::Scalar(255, 255, 0), 1);

                std::string frameInfo = "已处理: " + std::to_string(stream_info.frameCount) + " 帧";
                utils::putTextZH(display_frame, frameInfo,
                           cv::Point(textStartX, textStartY + 150),
                           0.5,
                           cv::Scalar(255, 255, 0), 1);
            }

            // 控制帧率 - 根据处理时间动态调整
            int sleepTime = std::max(1, 30 - static_cast<int>(stream_info.processingTime));
            std::this_thread::sleep_for(std::chrono::milliseconds(sleepTime));

        } catch (const std::exception& e) {
            std::cerr << "视频流 #" << (stream_info.id + 1) << " 处理异常: " << e.what() << std::endl;

            // 增加错误计数
            consecutiveErrorCount++;

            // 如果连续错误次数过多，标记为错误状态
            if (consecutiveErrorCount >= MAX_CONSECUTIVE_ERRORS) {
                std::cerr << "视频流 #" << (stream_info.id + 1) << " 连续错误次数过多，标记为错误状态" << std::endl;
                stream_info.hasError = true;

                // 在显示帧上显示严重错误信息
                std::lock_guard<std::mutex> lock(display_mutex);
                cv::Mat errorFrame(roi.height, roi.width, CV_8UC3, cv::Scalar(0, 0, 0));
                std::string errorMsg = "严重错误: " + std::string(e.what());
                utils::putTextZH(errorFrame, errorMsg,
                           cv::Point(20, roi.height/2),
                           0.7,
                           cv::Scalar(0, 0, 255), 2);

                // 显示重新加载提示
                std::string reloadMsg = "按Shift+" + std::to_string(stream_info.id + 1) + "重新加载";
                utils::putTextZH(errorFrame, reloadMsg,
                           cv::Point(20, roi.height/2 + 40),
                           0.5,
                           cv::Scalar(0, 255, 255), 1);

                errorFrame.copyTo(display_frame(roi));
            } else {
                // 在显示帧上显示一般错误信息
                std::lock_guard<std::mutex> lock(display_mutex);
                cv::Mat errorFrame(roi.height, roi.width, CV_8UC3, cv::Scalar(0, 0, 0));
                std::string errorMsg = "处理异常: " + std::string(e.what());
                utils::putTextZH(errorFrame, errorMsg,
                           cv::Point(20, roi.height/2),
                           0.5,
                           cv::Scalar(0, 0, 255), 1);
                errorFrame.copyTo(display_frame(roi));
            }

            // 等待一段时间后尝试恢复
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
}

int main(int argc, char* argv[]) {
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif

    // 初始化VisionFlow环境（在实际应用中需要替换为有效的许可证ID和服务器地址）
    core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "192.168.0.169");

    // 获取ProjectManager单例
    core::ProjectManager& projectManager = core::ProjectManager::get_instance();

    // 项目文件路径列表 - 可以根据需要添加或减少项目
    std::vector<std::string> projectPaths = {
        "C:/Users/<USER>/Downloads/test_video/projects/笔记本装配SOP.aivp",
        "C:/Users/<USER>/Downloads/test_video/projects/耳机鼠标.aivp",
        // "C:/Users/<USER>/Downloads/test_video/projects/笔记本装配SOP.aivp",
        // "C:/Users/<USER>/Downloads/test_video/projects/笔记本装配SOP.aivp"
    };

    // 视频路径列表 - 确保与项目路径列表长度一致
    std::vector<std::string> videoPaths = {
    "C:/Users/<USER>/Downloads/test_video/test_video/人员SOP监控/笔记本装配.mov",
    "C:/Users/<USER>/Downloads/test_video/test_video/人员SOP监控/耳机鼠标.mp4",
    "C:/Users/<USER>/Downloads/test_video/test_video/人员SOP监控/笔记本装配.mov",
    "C:/Users/<USER>/Downloads/test_video/test_video/人员SOP监控/笔记本装配.mov"
    };

    // 创建视频流信息列表
    std::vector<StreamInfo> streams;

    // 创建显示窗口
    cv::namedWindow("多路视频流处理", cv::WINDOW_NORMAL);
    cv::resizeWindow("多路视频流处理", 1280, 720);

    // 创建显示帧
    cv::Mat displayFrame(720, 1280, CV_8UC3, cv::Scalar(0, 0, 0));

    // 根据项目数量确定显示布局
    size_t streamCount = projectPaths.size();
    std::cout << "检测到 " << streamCount << " 个项目路径，将创建相应数量的视频流" << std::endl;

    // 根据视频流数量确定布局
    std::vector<cv::Rect> displayRois;

    if (streamCount <= 1) {
        // 单个视频流，使用整个窗口
        displayRois.push_back(cv::Rect(0, 0, 1280, 720));
    } else if (streamCount <= 2) {
        // 两个视频流，上下布局
        displayRois.push_back(cv::Rect(0, 0, 1280, 360));     // 上
        displayRois.push_back(cv::Rect(0, 360, 1280, 360));   // 下
    } else if (streamCount <= 4) {
        // 四个视频流，2x2布局
        displayRois.push_back(cv::Rect(0, 0, 640, 360));      // 左上
        displayRois.push_back(cv::Rect(640, 0, 640, 360));    // 右上
        displayRois.push_back(cv::Rect(0, 360, 640, 360));    // 左下
        displayRois.push_back(cv::Rect(640, 360, 640, 360));  // 右下
    } else if (streamCount <= 6) {
        // 六个视频流，3x2布局
        int width = 1280 / 3;
        int height = 720 / 2;
        displayRois.push_back(cv::Rect(0, 0, width, height));            // 左上
        displayRois.push_back(cv::Rect(width, 0, width, height));        // 中上
        displayRois.push_back(cv::Rect(width*2, 0, width, height));      // 右上
        displayRois.push_back(cv::Rect(0, height, width, height));       // 左下
        displayRois.push_back(cv::Rect(width, height, width, height));   // 中下
        displayRois.push_back(cv::Rect(width*2, height, width, height)); // 右下
    } else if (streamCount <= 9) {
        // 九个视频流，3x3布局
        int width = 1280 / 3;
        int height = 720 / 3;
        for (int row = 0; row < 3; row++) {
            for (int col = 0; col < 3; col++) {
                displayRois.push_back(cv::Rect(col * width, row * height, width, height));
            }
        }
    } else {
        // 超过9个视频流，使用4x4布局，最多支持16个
        int width = 1280 / 4;
        int height = 720 / 4;
        for (int row = 0; row < 4; row++) {
            for (int col = 0; col < 4; col++) {
                displayRois.push_back(cv::Rect(col * width, row * height, width, height));
            }
        }
        // 限制最多16个视频流
        streamCount = std::min(streamCount, (size_t)16);
    }

    // 确保displayRois的大小至少与streamCount相同
    if (displayRois.size() < streamCount) {
        std::cerr << "警告：显示区域数量(" << displayRois.size()
                  << ")小于视频流数量(" << streamCount
                  << ")，部分视频流将不会显示" << std::endl;
    }

    // 初始化每个视频流，根据projectPaths的大小确定视频流数量
    for (size_t i = 0; i < projectPaths.size(); i++) {
        StreamInfo streamInfo;
        streamInfo.id = i;
        streamInfo.projectPath = projectPaths[i];
        streamInfo.isActive = false;
        // 为每个视频流分配不同的结果存储服务器端口，从8888开始递增
        streamInfo.resultStoragePort = 8888 + i;

        // 打开项目
        std::cout << "正在打开项目 #" << (i + 1) << ": " << streamInfo.projectPath << std::endl;
        streamInfo.project = projectManager.open_project(streamInfo.projectPath);
        if (!streamInfo.project) {
            std::cerr << "无法打开项目: " << streamInfo.projectPath << std::endl;

            // 使用黑色图像代替
            cv::Mat blackFrame(360, 640, CV_8UC3, cv::Scalar(0, 0, 0));
            std::string errorMsg = "无法打开项目: " + streamInfo.projectPath;
            utils::putTextZH(blackFrame, errorMsg, cv::Point(20, 180),
                       0.5, cv::Scalar(0, 0, 255), 1);
            blackFrame.copyTo(displayFrame(displayRois[i]));

            streams.push_back(streamInfo);
            continue;
        }

        std::cout << "成功打开项目 #" << (i + 1) << ": " << streamInfo.project->get_name() << std::endl;

        // 设置视频URL
        streamInfo.videoUrl = videoPaths[i];

        // 创建视频处理核心 - 每个视频流使用完全独立的实例
        streamInfo.processor = std::make_shared<core::VideoProcessingCore>();

        // 创建 Python 脚本管理器并加载 Python 脚本插件
        streamInfo.scriptManager = std::make_shared<ai::plugins::PythonScriptManager>("scripts/task_plugins");

        // 设置项目的结果存储服务器端口
        std::cout << "设置视频流 #" << (i + 1) << " 的结果存储服务器端口: " << streamInfo.resultStoragePort << std::endl;
        streamInfo.project->set_result_storage_tcp_port(streamInfo.resultStoragePort);

        // 启用结果存储
        streamInfo.project->set_enable_result_storage(false);

        // 将项目配置导入到处理核心
        projectManager.import_to_video_processing_core(streamInfo.project, streamInfo.processor);

        // 确认结果存储服务器已启动并显示端口
        auto resultServer = streamInfo.processor->get_result_storage_server();
        if (resultServer && resultServer->is_running()) {
            std::cout << "视频流 #" << (i + 1) << " 的结果存储服务器已启动，端口: "
                      << resultServer->get_port() << std::endl;
        } else {
            std::cout << "视频流 #" << (i + 1) << " 的结果存储服务器未启动，手动启动..." << std::endl;
            streamInfo.processor->start_result_storage_server(
                "results/stream_" + std::to_string(i + 1),
                core::VideoResultStorageServer::StorageMode::IMMEDIATE,
                streamInfo.resultStoragePort,
                5000
            );
        }

        // 打开视频源
        bool opened = false;

        // 检查URL类型并打开相应的视频源
        std::string url = streamInfo.videoUrl;
        if (url.find("rtsp://") == 0) {
            // RTSP流
            opened = streamInfo.processor->open_rtsp_stream(url);
            std::cout << "视频流 #" << (i + 1) << " 尝试打开RTSP流: " << url << std::endl;
        } else {
            // 视频文件
            opened = streamInfo.processor->open_video_file(url);
            std::cout << "视频流 #" << (i + 1) << " 尝试打开视频文件: " << url << std::endl;
        }

        if (!opened) {
            std::cerr << "视频流 #" << (i + 1) << " 无法打开视频源: " << url << std::endl;
            // 使用黑色图像代替
            cv::Mat blackFrame(360, 640, CV_8UC3, cv::Scalar(0, 0, 0));
            std::string errorMsg = "无法打开视频源";
            utils::putTextZH(blackFrame, errorMsg, cv::Point(50, 180),
                       1.0, cv::Scalar(0, 0, 255), 2);
            blackFrame.copyTo(displayFrame(displayRois[i]));

            streams.push_back(streamInfo);
            continue;
        }

        // 标记为激活状态
        streamInfo.isActive = true;
        streams.push_back(streamInfo);
    }

    // 创建线程控制变量
    std::atomic<bool> running(true);
    std::mutex displayMutex;

    // 创建处理线程
    std::vector<std::thread> threads;
    for (size_t i = 0; i < streams.size(); i++) {
        if (streams[i].isActive) {
            // 确保不超出displayRois的范围
            if (i < displayRois.size()) {
                threads.emplace_back(process_stream, std::ref(streams[i]), std::ref(running),
                                    std::ref(displayMutex), std::ref(displayFrame),
                                    displayRois[i]);
                std::cout << "已创建视频流 #" << (i + 1) << " 的处理线程" << std::endl;
            } else {
                std::cerr << "警告：视频流 #" << (i + 1) << " 没有对应的显示区域，跳过创建处理线程" << std::endl;
                // 将流标记为非活动状态，因为无法显示
                streams[i].isActive = false;
            }
        }
    }

    // 添加帮助信息
    cv::Mat helpFrame(720, 1280, CV_8UC3, cv::Scalar(0, 0, 0));
    std::vector<std::string> helpText = {
        "键盘控制:",
        "ESC - 退出程序",
        "H - 显示/隐藏帮助",
        "1-9 - 暂停/继续对应视频流",
        "Shift+1-9 - 重新加载对应视频流",
        "S - 保存当前帧",
        "J - 保存视频流信息到JSON",
        "R - 重置所有视频流",
        "F - 切换全屏模式"
    };

    for (size_t i = 0; i < helpText.size(); i++) {
        utils::putTextZH(helpFrame, helpText[i],
                   cv::Point(480, 250 + i * 30),
                   0.7,
                   cv::Scalar(255, 255, 255), 1);
    }

    // 状态变量
    bool showHelp = false;
    bool fullscreenMode = false;
    int selectedStream = -1;

    // 主循环 - 显示合并后的帧
    while (true) {
        // 检查是否所有视频都已结束
        bool allVideosEnded = true;
        for (const auto& stream : streams) {
            if (stream.isActive && !stream.isVideoEnded && !stream.hasError) {
                allVideosEnded = false;
                break;
            }
        }

        // 如果所有视频都已结束，显示提示信息
        if (allVideosEnded && !streams.empty()) {
            std::lock_guard<std::mutex> lock(displayMutex);
            cv::Mat endFrame(720, 1280, CV_8UC3, cv::Scalar(0, 0, 0));
            std::string endMsg = "所有视频播放结束";
            utils::putTextZH(endFrame, endMsg,
                       cv::Point(480, 300),
                       1.0,
                       cv::Scalar(255, 255, 255), 2);

            std::string exitMsg = "按ESC键退出程序";
            utils::putTextZH(endFrame, exitMsg,
                       cv::Point(480, 350),
                       0.7,
                       cv::Scalar(0, 255, 255), 1);

            cv::imshow("多路视频流处理", endFrame);
            int key = cv::waitKey(30);
            if (key == 27) { // ESC键
                goto exit_loop;
            }
            continue;
        }
        // 创建显示帧的副本
        cv::Mat displayCopy;
        {
            std::lock_guard<std::mutex> lock(displayMutex);
            displayCopy = displayFrame.clone();
        }

        // 如果显示帮助，叠加帮助信息
        if (showHelp) {
            // 创建半透明覆盖层
            cv::Mat overlay = displayCopy.clone();
            cv::addWeighted(helpFrame, 0.7, overlay, 0.3, 0, displayCopy);
        }

        // 如果有选中的视频流，高亮显示
        if (selectedStream >= 0 && selectedStream < displayRois.size()) {
            cv::rectangle(displayCopy, displayRois[selectedStream], cv::Scalar(0, 0, 255), 3);
        }

        // 显示帧
        cv::imshow("多路视频流处理", displayCopy);

        // 等待按键
        int key = cv::waitKey(30);

        // 处理按键
        if (key != -1) {
            switch (key) {
                case 27: // ESC键 - 退出
                    goto exit_loop;

                case 'h': // H键 - 显示/隐藏帮助
                case 'H':
                    showHelp = !showHelp;
                    break;

                case 'f': // F键 - 切换全屏模式
                case 'F':
                    fullscreenMode = !fullscreenMode;
                    if (fullscreenMode) {
                        cv::setWindowProperty("多路视频流处理", cv::WND_PROP_FULLSCREEN, cv::WINDOW_FULLSCREEN);
                    } else {
                        cv::setWindowProperty("多路视频流处理", cv::WND_PROP_FULLSCREEN, cv::WINDOW_NORMAL);
                    }
                    break;

                case 's': // S键 - 保存当前帧
                case 'S':
                    {
                        std::string filename = "multi_stream_" +
                            std::to_string(std::chrono::system_clock::now().time_since_epoch().count()) + ".jpg";
                        cv::imwrite(filename, displayCopy);
                        std::cout << "已保存当前帧到: " << filename << std::endl;
                    }
                    break;

                case 'j': // J键 - 保存视频流信息到JSON
                case 'J':
                    {
                        std::string filename = "stream_info_" +
                            std::to_string(std::chrono::system_clock::now().time_since_epoch().count()) + ".json";
                        if (save_stream_info_to_json(streams, filename)) {
                            std::cout << "已保存视频流信息到: " << filename << std::endl;
                        } else {
                            std::cerr << "保存视频流信息失败" << std::endl;
                        }
                    }
                    break;

                case 'r': // R键 - 重置所有视频流
                case 'R':
                    for (auto& stream : streams) {
                        if (stream.isActive) {
                            stream.isPaused = false;
                        }
                    }
                    std::cout << "已重置所有视频流" << std::endl;
                    break;

                case '1': // 1-9键 - 暂停/继续对应视频流
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                    {
                        int streamIndex = key - '1';
                        if (streamIndex >= 0 && streamIndex < streams.size() && streams[streamIndex].isActive) {
                            streams[streamIndex].isPaused = !streams[streamIndex].isPaused;
                            std::cout << "视频流 #" << (streamIndex + 1) << " "
                                     << (streams[streamIndex].isPaused ? "已暂停" : "已继续") << std::endl;

                            // 更新选中的视频流
                            selectedStream = streamIndex;
                        } else {
                            std::cout << "视频流 #" << (streamIndex + 1) << " 不存在或未激活" << std::endl;
                        }
                    }
                    break;

                case '!': // Shift+1 - 重新加载视频流1
                case '@': // Shift+2 - 重新加载视频流2
                case '#': // Shift+3 - 重新加载视频流3
                case '$': // Shift+4 - 重新加载视频流4
                case '%': // Shift+5 - 重新加载视频流5
                case '^': // Shift+6 - 重新加载视频流6
                case '&': // Shift+7 - 重新加载视频流7
                case '*': // Shift+8 - 重新加载视频流8
                case '(': // Shift+9 - 重新加载视频流9
                    {
                        int streamIndex = -1;
                        if (key == '!') streamIndex = 0;
                        else if (key == '@') streamIndex = 1;
                        else if (key == '#') streamIndex = 2;
                        else if (key == '$') streamIndex = 3;
                        else if (key == '%') streamIndex = 4;
                        else if (key == '^') streamIndex = 5;
                        else if (key == '&') streamIndex = 6;
                        else if (key == '*') streamIndex = 7;
                        else if (key == '(') streamIndex = 8;

                        if (streamIndex >= 0 && streamIndex < streams.size()) {
                            std::cout << "正在重新加载视频流 #" << (streamIndex + 1) << "..." << std::endl;

                            // 显示重新加载信息
                            if (streamIndex < displayRois.size()) {
                                std::lock_guard<std::mutex> lock(displayMutex);
                                cv::Mat loadingFrame(displayRois[streamIndex].height, displayRois[streamIndex].width,
                                                   CV_8UC3, cv::Scalar(0, 0, 0));
                                std::string loadingMsg = "正在重新加载视频流...";
                                utils::putTextZH(loadingFrame, loadingMsg,
                                           cv::Point(20, loadingFrame.rows/2),
                                           0.7,
                                           cv::Scalar(0, 255, 255), 2);
                                loadingFrame.copyTo(displayFrame(displayRois[streamIndex]));
                            }

                            // 重新加载视频流
                            try {
                                if (reload_stream(streams[streamIndex])) {
                                    std::cout << "视频流 #" << (streamIndex + 1) << " 重新加载成功" << std::endl;
                                } else {
                                    std::cerr << "视频流 #" << (streamIndex + 1) << " 重新加载失败" << std::endl;

                                    // 显示错误信息
                                    if (streamIndex < displayRois.size()) {
                                        std::lock_guard<std::mutex> lock(displayMutex);
                                        cv::Mat errorFrame(displayRois[streamIndex].height, displayRois[streamIndex].width,
                                                         CV_8UC3, cv::Scalar(0, 0, 0));
                                        std::string errorMsg = "重新加载失败";
                                        utils::putTextZH(errorFrame, errorMsg,
                                                   cv::Point(20, errorFrame.rows/2),
                                                   0.7,
                                                   cv::Scalar(0, 0, 255), 2);
                                        errorFrame.copyTo(displayFrame(displayRois[streamIndex]));
                                    }
                                }
                            } catch (const std::exception& e) {
                                std::cerr << "视频流 #" << (streamIndex + 1) << " 重新加载时发生异常: " << e.what() << std::endl;

                                // 显示错误信息
                                if (streamIndex < displayRois.size()) {
                                    std::lock_guard<std::mutex> lock(displayMutex);
                                    cv::Mat errorFrame(displayRois[streamIndex].height, displayRois[streamIndex].width,
                                                     CV_8UC3, cv::Scalar(0, 0, 0));
                                    std::string errorMsg = "重新加载异常: " + std::string(e.what());
                                    utils::putTextZH(errorFrame, errorMsg,
                                               cv::Point(20, errorFrame.rows/2),
                                               0.5,
                                               cv::Scalar(0, 0, 255), 1);
                                    errorFrame.copyTo(displayFrame(displayRois[streamIndex]));
                                }
                            }

                            // 更新选中的视频流
                            selectedStream = streamIndex;
                        } else {
                            std::cout << "视频流 #" << (streamIndex + 1) << " 不存在" << std::endl;
                        }
                    }
                    break;
            }
        }
    }

exit_loop:

    // 停止所有线程
    running = false;

    // 等待所有线程结束，但设置超时时间，避免卡死
    for (auto& thread : threads) {
        if (thread.joinable()) {
            // 使用超时等待，避免某个线程卡死导致整个程序无法退出
            std::thread watchdog([&]() {
                std::this_thread::sleep_for(std::chrono::seconds(3));
                std::cout << "等待线程结束超时，强制退出程序" << std::endl;
                std::exit(0);
            });
            watchdog.detach();

            thread.join();
        }
    }

    // 关闭所有视频源
    for (auto& stream : streams) {
        if (stream.processor) {
            try {
                stream.processor->close_video();
            } catch (const std::exception& e) {
                std::cerr << "关闭视频流 #" << (stream.id + 1) << " 时发生异常: " << e.what() << std::endl;
            }
        }
    }

    // 关闭窗口
    cv::destroyAllWindows();

    return 0;
}
